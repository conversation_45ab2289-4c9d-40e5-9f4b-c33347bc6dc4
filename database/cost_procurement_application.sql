-- 采购申请数据库表设计
-- 创建时间: 2025-08-26
-- 描述: 采购申请主表和明细表，用于管理物料采购申请流程

-- 采购申请主表
CREATE TABLE `cost_procurement_application`
(
    `id`                                VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 申请单信息
    `application_no`                    VARCHAR(50)  NOT NULL COMMENT '采购申请单号（自动生成）',
    
    -- 物料信息
    `material_code`                     VARCHAR(50)  NOT NULL COMMENT '物料编码',
    `material_name`                     VARCHAR(100) NOT NULL COMMENT '物料名称',
    `unit`                              VARCHAR(20)  NOT NULL COMMENT '计量单位',
    
    -- 时间周期信息
    `quarter`                           VARCHAR(20)  NOT NULL COMMENT '所在季度(如：2025年第一季度)',
    `start_date`                        VARCHAR(10)  NOT NULL COMMENT '开始时间（年月，如：2025-01）',
    `end_date`                          VARCHAR(10)  NOT NULL COMMENT '结束时间（年月，如：2025-03）',
    
    -- 预算数量信息
    `budget_total_quantity`             DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '预算总量',
    `purchased_quantity`                DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '已采购量',
    `approval_quantity`                 DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '审批中量',
    `available_quantity`                DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '可采购量',
    `current_purchase_quantity`         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '本次采购量',
    
    -- 价格信息
    `unit_price_excluding_tax`          DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税单价（元）',
    `total_price_excluding_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价（元）',
    `total_price_including_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '含税总价（元）',
    `tax_amount`                        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '税额（元）',
    
    -- 关联信息
    `execution_basis_id`                VARCHAR(36)  NULL COMMENT '关联采购执行依据主表ID',
    
    -- 审计字段
    `create_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                         VARCHAR(50)  NOT NULL COMMENT '创建人',
    `update_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                         VARCHAR(50)  NULL COMMENT '更新人',
    
    -- 多租户字段
    `tenant_id`                         INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                      VARCHAR(64)  NULL COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_application_no` (`application_no`, `tenant_id`, `del_flag`),
    KEY `idx_material_code` (`material_code`),
    KEY `idx_material_name` (`material_name`),
    KEY `idx_quarter` (`quarter`),
    KEY `idx_execution_basis_id` (`execution_basis_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购申请主表';

-- 采购申请明细表
CREATE TABLE `cost_procurement_application_detail`
(
    `id`                                VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 关联信息
    `application_id`                    VARCHAR(36)  NOT NULL COMMENT '关联采购申请主表ID',
    
    -- 预算信息
    `budget_code`                       VARCHAR(50)  NOT NULL COMMENT '预算编码',
    `budget_name`                       VARCHAR(100) NOT NULL COMMENT '预算名称',
    
    -- 数量信息
    `budget_total_quantity`             DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '预算总量',
    `available_quantity`                DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '可采购量',
    `current_purchase_quantity`         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '本次采购量',
    
    -- 价格信息
    `unit_price_excluding_tax`          DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税单价（元）',
    `total_price_excluding_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价（元）',
    `total_price_including_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '含税总价（元）',
    `tax_amount`                        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '税额（元）',
    -- 审计字段
    `create_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                         VARCHAR(50)  NOT NULL COMMENT '创建人',
    `update_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                         VARCHAR(50)  NULL COMMENT '更新人',
    
    -- 多租户字段
    `tenant_id`                         INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                      VARCHAR(64)  NULL COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    KEY `idx_application_id` (`application_id`),
    KEY `idx_budget_code` (`budget_code`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购申请明细表';

-- 添加表注释说明
ALTER TABLE `cost_procurement_application` COMMENT = '采购申请主表：用于管理物料采购申请信息，记录采购申请的基本信息、数量、价格和审批状态';
ALTER TABLE `cost_procurement_application_detail` COMMENT = '采购申请明细表：记录每个采购申请下的预算明细信息，支持按预算项目分配采购量';


-- 添加索引优化查询性能
CREATE INDEX `idx_application_material_quarter` ON `cost_procurement_application` (`material_code`, `quarter`, `tenant_id`, `del_flag`);
CREATE INDEX `idx_detail_budget_application` ON `cost_procurement_application_detail` (`budget_code`, `application_id`, `tenant_id`, `del_flag`);

-- 业务逻辑说明
/*
1. 采购申请单号生成规则：
   - 格式：PA + YYYYMMDD + 4位序号
   - 示例：PA202508260001
   - 需要在应用层实现自动生成逻辑

2. 数量校验规则：
   - 本次采购量必须 <= 可采购量
   - 可采购量 = 预算总量 - 已采购量
   - 明细表中各预算项目的本次采购量之和 = 主表的本次采购量

3. 价格计算规则：
   - 不含税总价 = 本次采购量 × 不含税单价
   - 含税总价 = 不含税总价 × (1 + 税率)，默认税率13%
   - 税额 = 含税总价 - 不含税总价

4. 加权均分逻辑：
   - 明细行本次采购量 = (明细行预算总量 / 主表预算总量) × 主表本次采购量
   - 需要处理小数精度问题，确保明细行采购量之和等于主表采购量

6. 数据来源：
   - 从采购执行依据列表页点击"采购申请"按钮：自动填充执行依据数据
   - 从采购申请列表页点击"新增"按钮：根据选择的物料和季度查询对应的执行依据

7. 字段对应关系：
   - 与采购执行依据主表字段保持一致：material_code, material_name, unit, quarter, start_date, end_date
   - 预算相关字段：budget_total_quantity对应total_demand_quantity
   - 价格相关字段：unit_price_excluding_tax, total_price_excluding_tax, total_price_including_tax
*/
