package com.cdkit.modules.cm.domain.procurement.repository;

import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;

import java.util.List;

/**
 * 采购申请仓储接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface ProcurementApplicationRepository {

    /**
     * 保存采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 保存后的实体
     */
    ProcurementApplicationEntity save(ProcurementApplicationEntity entity);

    /**
     * 更新采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 更新后的实体
     */
    ProcurementApplicationEntity update(ProcurementApplicationEntity entity);

    /**
     * 根据ID查询采购申请详情（包含明细）
     * 
     * @param id 采购申请ID
     * @return 采购申请实体
     */
    ProcurementApplicationEntity getById(String id);

    /**
     * 根据ID删除采购申请
     * 
     * @param id 采购申请ID
     */
    void deleteById(String id);

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     */
    void deleteBatch(List<String> ids);

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     * 
     * @return 采购申请单号
     */
    String generateApplicationNo();

    /**
     * 根据申请单号查询采购申请
     * 
     * @param applicationNo 申请单号
     * @return 采购申请实体
     */
    ProcurementApplicationEntity findByApplicationNo(String applicationNo);

    /**
     * 检查申请单号是否存在
     *
     * @param applicationNo 申请单号
     * @return 是否存在
     */
    boolean existsByApplicationNo(String applicationNo);

    /**
     * 更新采购执行依据的已采购量
     * 根据采购申请的信息更新对应的采购执行依据主表和明细表的已采购量
     *
     * @param entity 采购申请实体
     */
    void updateExecutionBasisPurchasedQuantity(ProcurementApplicationEntity entity);
}
