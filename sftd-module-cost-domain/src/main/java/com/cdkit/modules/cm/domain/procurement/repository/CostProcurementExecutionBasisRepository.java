package com.cdkit.modules.cm.domain.procurement.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;

import java.util.List;

/**
 * 采购执行依据仓储接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface CostProcurementExecutionBasisRepository {

    /**
     * 分页查询采购执行依据列表
     * 
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<CostProcurementExecutionBasisEntity> queryPageList(CostProcurementExecutionBasisEntity queryEntity, PageReq pageReq);

    /**
     * 根据ID查询采购执行依据详情（包含明细）
     * 
     * @param id 采购执行依据ID
     * @return 采购执行依据实体
     */
    CostProcurementExecutionBasisEntity getById(String id);

    /**
     * 保存采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 保存后的实体
     */
    CostProcurementExecutionBasisEntity save(CostProcurementExecutionBasisEntity entity);

    /**
     * 更新采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 更新后的实体
     */
    CostProcurementExecutionBasisEntity update(CostProcurementExecutionBasisEntity entity);

    /**
     * 根据ID删除采购执行依据
     * 
     * @param id 采购执行依据ID
     */
    void deleteById(String id);

    /**
     * 批量删除采购执行依据
     * 
     * @param ids 采购执行依据ID列表
     */
    void deleteBatch(List<String> ids);

    /**
     * 根据查询条件获取所有数据（用于导出）
     *
     * @param queryEntity 查询条件
     * @return 采购执行依据列表
     */
    List<CostProcurementExecutionBasisEntity> findAll(CostProcurementExecutionBasisEntity queryEntity);

    /**
     * 根据物料编码查询采购执行依据信息（包含明细）
     * 一个物料编码可能对应多个不同的季度数据
     *
     * @param materialCode 物料编码
     * @return 采购执行依据列表（包含所有季度的数据）
     */
    List<CostProcurementExecutionBasisEntity> findByMaterialCode(String materialCode);

    /**
     * 根据物料编码和季度查询采购执行依据信息（包含明细）
     *
     * @param materialCode 物料编码
     * @param quarter 所在季度
     * @return 采购执行依据实体
     */
    CostProcurementExecutionBasisEntity findByMaterialCodeAndQuarter(String materialCode, String quarter);
}
