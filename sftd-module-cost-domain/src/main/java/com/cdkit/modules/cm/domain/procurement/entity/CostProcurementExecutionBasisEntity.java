package com.cdkit.modules.cm.domain.procurement.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 采购执行依据主表
 * @Author: cdkit-boot
 * @Date:   2025-08-21
 * @Version: V1.0
 */
@Schema(description="cost_procurement_execution_basis对象")
@Data
@TableName("cost_procurement_execution_basis")
public class CostProcurementExecutionBasisEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private java.lang.String id;
    /**物料编码*/
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private java.lang.String materialCode;
    /**物料名称*/
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private java.lang.String materialName;
    /**计量单位*/
    @Excel(name = "计量单位", width = 15)
    @Schema(description = "计量单位")
    private java.lang.String unit;
    /**所在季度(如：2025年第一季度)*/
    @Excel(name = "所在季度(如：2025年第一季度)", width = 15)
    @Schema(description = "所在季度(如：2025年第一季度)")
    private java.lang.String quarter;
    /**开始时间（年月）*/
    @Excel(name = "开始时间（年月）", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "开始时间（年月）")
    private java.util.Date startDate;
    /**结束时间（年月）*/
    @Excel(name = "结束时间（年月）", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "结束时间（年月）")
    private java.util.Date endDate;
    /**总需求量*/
    @Excel(name = "总需求量", width = 15)
    @Schema(description = "总需求量")
    private java.math.BigDecimal totalDemandQuantity;
    /**不含税单价（元）*/
    @Excel(name = "不含税单价（元）", width = 15)
    @Schema(description = "不含税单价（元）")
    private java.math.BigDecimal unitPriceExcludingTax;
    /**不含税总价（元）*/
    @Excel(name = "不含税总价（元）", width = 15)
    @Schema(description = "不含税总价（元）")
    private java.math.BigDecimal totalPriceExcludingTax;
    /**含税总价（元）*/
    @Excel(name = "含税总价（元）", width = 15)
    @Schema(description = "含税总价（元）")
    private java.math.BigDecimal totalPriceIncludingTax;
    /**已采购量*/
    @Excel(name = "已采购量", width = 15)
    @Schema(description = "已采购量")
    private java.math.BigDecimal purchasedQuantity;
    /**已支出不含税金额（元）*/
    @Excel(name = "已支出不含税金额（元）", width = 15)
    @Schema(description = "已支出不含税金额（元）")
    private java.math.BigDecimal spentAmountExcludingTax;
    /**已支出含税金额（元）*/
    @Excel(name = "已支出含税金额（元）", width = 15)
    @Schema(description = "已支出含税金额（元）")
    private java.math.BigDecimal spentAmountIncludingTax;
    /**剩余不含税金额（元）*/
    @Excel(name = "剩余不含税金额（元）", width = 15)
    @Schema(description = "剩余不含税金额（元）")
    private java.math.BigDecimal remainingAmountExcludingTax;
    /**剩余含税金额（元）*/
    @Excel(name = "剩余含税金额（元）", width = 15)
    @Schema(description = "剩余含税金额（元）")
    private java.math.BigDecimal remainingAmountIncludingTax;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private java.util.Date createTime;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private java.util.Date updateTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**租户ID*/
    @Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private java.lang.Integer tenantId;
    /**删除标识 0:未删除 1:删除*/
    @Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    private java.lang.Integer delFlag;
    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private java.lang.String sysOrgCode;

    @ExcelCollection(name="采购执行依据明细表")
    @Schema(description = "采购执行依据明细表")
    private List<CostProcurementExecutionBasisDetailEntity> costProcurementExecutionBasisDetailList;
}
