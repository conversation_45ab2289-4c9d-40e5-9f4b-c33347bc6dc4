package com.cdkit.modules.cm.api.procurement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购执行依据DTO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Accessors(chain = true)
@Schema(description = "采购执行依据")
public class CostProcurementExecutionBasisDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    @Schema(description = "UUID主键")
    private String id;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称", width = 20)
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位", width = 10)
    @Schema(description = "计量单位")
    private String unit;

    /**
     * 所在季度(如：2025年第一季度)
     */
    @Excel(name = "所在季度", width = 20)
    @Schema(description = "所在季度")
    private String quarter;

    /**
     * 开始时间（年月）
     */
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd")
    @Schema(description = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间（年月）
     */
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd")
    @Schema(description = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 总需求量
     */
    @Excel(name = "总需求量", width = 15)
    @Schema(description = "总需求量")
    private BigDecimal totalDemandQuantity;

    /**
     * 不含税单价（元）
     */
    @Excel(name = "不含税单价（元）", width = 15)
    @Schema(description = "不含税单价（元）")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    @Excel(name = "不含税总价（元）", width = 15)
    @Schema(description = "不含税总价（元）")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    @Excel(name = "含税总价（元）", width = 15)
    @Schema(description = "含税总价（元）")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    @Excel(name = "已采购量", width = 15)
    @Schema(description = "已采购量")
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额（元）
     */
    @Excel(name = "已支出不含税金额（元）", width = 18)
    @Schema(description = "已支出不含税金额（元）")
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额（元）
     */
    @Excel(name = "已支出含税金额（元）", width = 18)
    @Schema(description = "已支出含税金额（元）")
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额（元）
     */
    @Excel(name = "剩余不含税金额（元）", width = 18)
    @Schema(description = "剩余不含税金额（元）")
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额（元）
     */
    @Excel(name = "剩余含税金额（元）", width = 18)
    @Schema(description = "剩余含税金额（元）")
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**
     * 删除标识 0:未删除 1:删除
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    /**
     * 明细列表
     */
    @ExcelCollection(name = "采购执行依据明细")
    @Schema(description = "明细列表")
    private List<CostProcurementExecutionBasisDetailDTO> details;
}
