package com.cdkit.modules.cm.api.procurement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购执行依据明细DTO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Accessors(chain = true)
@Schema(description = "采购执行依据明细")
public class CostProcurementExecutionBasisDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    @Schema(description = "UUID主键")
    private String id;

    /**
     * 执行依据主表ID
     */
    @Schema(description = "执行依据主表ID")
    private String executionBasisId;

    /**
     * 预算编号
     */
    @Excel(name = "预算编号", width = 20)
    @Schema(description = "预算编号")
    private String quarterlyBudgetNo;

    /**
     * 预算名称
     */
    @Excel(name = "预算名称", width = 25)
    @Schema(description = "预算名称")
    private String quarterlyBudgetName;

    /**
     * 总需求量
     */
    @Excel(name = "总需求量", width = 15)
    @Schema(description = "总需求量")
    private BigDecimal demandQuantity;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位", width = 10)
    @Schema(description = "计量单位")
    private String unit;

    /**
     * 不含税单价（元）
     */
    @Excel(name = "不含税单价（元）", width = 15)
    @Schema(description = "不含税单价（元）")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    @Excel(name = "不含税总价（元）", width = 15)
    @Schema(description = "不含税总价（元）")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    @Excel(name = "含税总价（元）", width = 15)
    @Schema(description = "含税总价（元）")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    @Excel(name = "已采购量", width = 15)
    @Schema(description = "已采购量")
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额（元）
     */
    @Excel(name = "已支出不含税金额（元）", width = 18)
    @Schema(description = "已支出不含税金额（元）")
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额（元）
     */
    @Excel(name = "已支出含税金额（元）", width = 18)
    @Schema(description = "已支出含税金额（元）")
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额（元）
     */
    @Excel(name = "剩余不含税金额（元）", width = 18)
    @Schema(description = "剩余不含税金额（元）")
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额（元）
     */
    @Excel(name = "剩余含税金额（元）", width = 18)
    @Schema(description = "剩余含税金额（元）")
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**
     * 删除标识 0:未删除 1:删除
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
