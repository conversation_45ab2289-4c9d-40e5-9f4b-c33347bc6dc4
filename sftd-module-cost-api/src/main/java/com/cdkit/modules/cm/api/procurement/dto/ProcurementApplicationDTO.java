package com.cdkit.modules.cm.api.procurement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购申请DTO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购申请")
public class ProcurementApplicationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    @Schema(description = "UUID主键")
    private String id;

    /**
     * 采购申请单号（自动生成）
     */
    @Schema(description = "采购申请单号", example = "PA202508260001")
    private String applicationNo;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @Schema(description = "物料编码", example = "MAT001")
    private String materialCode;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不能为空")
    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    /**
     * 计量单位
     */
    @NotBlank(message = "计量单位不能为空")
    @Schema(description = "计量单位", example = "kg")
    private String unit;

    /**
     * 所在季度
     */
    @NotBlank(message = "所在季度不能为空")
    @Schema(description = "所在季度", example = "2025年第一季度")
    private String quarter;

    /**
     * 开始时间（年月）
     */
    @NotBlank(message = "开始时间不能为空")
    @Schema(description = "开始时间", example = "2025-01")
    private String startDate;

    /**
     * 结束时间（年月）
     */
    @NotBlank(message = "结束时间不能为空")
    @Schema(description = "结束时间", example = "2025-03")
    private String endDate;

    /**
     * 预算总量
     */
    @NotNull(message = "预算总量不能为空")
    @DecimalMin(value = "0", message = "预算总量不能为负数")
    @Schema(description = "预算总量", example = "1000.0000")
    private BigDecimal budgetTotalQuantity;

    /**
     * 已采购量
     */
    @NotNull(message = "已采购量不能为空")
    @DecimalMin(value = "0", message = "已采购量不能为负数")
    @Schema(description = "已采购量", example = "200.0000")
    private BigDecimal purchasedQuantity;

    /**
     * 可采购量
     */
    @NotNull(message = "可采购量不能为空")
    @DecimalMin(value = "0", message = "可采购量不能为负数")
    @Schema(description = "可采购量", example = "800.0000")
    private BigDecimal availableQuantity;

    /**
     * 本次采购量
     */
    @NotNull(message = "本次采购量不能为空")
    @DecimalMin(value = "0.0001", message = "本次采购量必须大于0")
    @Schema(description = "本次采购量", example = "500.0000")
    private BigDecimal currentPurchaseQuantity;

    /**
     * 不含税单价（元）
     */
    @NotNull(message = "不含税单价不能为空")
    @DecimalMin(value = "0", message = "不含税单价不能为负数")
    @Schema(description = "不含税单价", example = "10.500000")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    @Schema(description = "不含税总价", example = "5250.00")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    @Schema(description = "含税总价", example = "5932.50")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 税额（元）
     */
    @Schema(description = "税额", example = "682.50")
    private BigDecimal taxAmount;



    /**
     * 关联采购执行依据主表ID
     */
    @Schema(description = "关联采购执行依据主表ID")
    private String executionBasisId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    /**
     * 明细列表
     */
    @Valid
    @NotEmpty(message = "明细列表不能为空")
    @Schema(description = "明细列表")
    private List<ProcurementApplicationDetailDTO> details;
}
