package com.cdkit.modules.cm.application.procurement;

import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationDetailEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementApplicationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 采购申请应用服务测试类
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@ExtendWith(MockitoExtension.class)
class ProcurementApplicationServiceTest {

    @Mock
    private ProcurementApplicationRepository procurementApplicationRepository;

    @InjectMocks
    private ProcurementApplicationService procurementApplicationService;

    private ProcurementApplicationEntity testEntity;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .applicationNo("PA202508260001")
                .materialCode("MAT001")
                .materialName("钢材")
                .unit("kg")
                .quarter("2025年第一季度")
                .startDate("2025-01")
                .endDate("2025-03")
                .budgetTotalQuantity(new BigDecimal("1000.0000"))
                .purchasedQuantity(new BigDecimal("200.0000"))
                .availableQuantity(new BigDecimal("800.0000"))
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .unitPriceExcludingTax(new BigDecimal("10.500000"))
                .executionBasisId("exec-basis-id-123")
                .createTime(new Date())
                .createBy("test-user")
                .tenantId(1)
                .delFlag(0)
                .build();

        // 创建明细数据
        ProcurementApplicationDetailEntity detail1 = ProcurementApplicationDetailEntity.builder()
                .id("detail-1")
                .applicationId("test-app-id")
                .budgetCode("BUD001")
                .budgetName("原材料预算")
                .budgetTotalQuantity(new BigDecimal("500.0000"))
                .availableQuantity(new BigDecimal("400.0000"))
                .currentPurchaseQuantity(new BigDecimal("250.0000"))
                .unitPriceExcludingTax(new BigDecimal("10.500000"))
                .executionBasisDetailId("detail-id-123")
                .quarterlyBudgetId("budget-id-123")
                .build();

        ProcurementApplicationDetailEntity detail2 = ProcurementApplicationDetailEntity.builder()
                .id("detail-2")
                .applicationId("test-app-id")
                .budgetCode("BUD002")
                .budgetName("设备采购预算")
                .budgetTotalQuantity(new BigDecimal("500.0000"))
                .availableQuantity(new BigDecimal("400.0000"))
                .currentPurchaseQuantity(new BigDecimal("250.0000"))
                .unitPriceExcludingTax(new BigDecimal("10.500000"))
                .executionBasisDetailId("detail-id-124")
                .quarterlyBudgetId("budget-id-124")
                .build();

        testEntity.setDetails(Arrays.asList(detail1, detail2));
    }

    @Test
    void testSave_Success() {
        // 准备测试数据
        ProcurementApplicationEntity savedEntity = ProcurementApplicationEntity.builder()
                .id("saved-app-id")
                .applicationNo("PA202508260001")
                .materialCode("MAT001")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .quarter("2025年第一季度")
                .build();

        // 模拟Repository行为
        when(procurementApplicationRepository.save(any(ProcurementApplicationEntity.class)))
                .thenReturn(savedEntity);
        doNothing().when(procurementApplicationRepository)
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));

        // 执行测试
        ProcurementApplicationEntity result = procurementApplicationService.save(testEntity);

        // 验证结果
        assertNotNull(result);
        assertEquals("saved-app-id", result.getId());
        assertEquals("PA202508260001", result.getApplicationNo());

        // 验证方法调用
        verify(procurementApplicationRepository, times(1)).save(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, times(1))
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));
    }

    @Test
    void testSave_UpdateExecutionBasisFailed() {
        // 准备测试数据
        ProcurementApplicationEntity savedEntity = ProcurementApplicationEntity.builder()
                .id("saved-app-id")
                .applicationNo("PA202508260001")
                .materialCode("MAT001")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .quarter("2025年第一季度")
                .build();

        // 模拟Repository行为
        when(procurementApplicationRepository.save(any(ProcurementApplicationEntity.class)))
                .thenReturn(savedEntity);
        doThrow(new RuntimeException("更新采购执行依据失败"))
                .when(procurementApplicationRepository)
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            procurementApplicationService.save(testEntity);
        });

        assertTrue(exception.getMessage().contains("保存采购申请成功，但更新采购执行依据失败"));

        // 验证方法调用
        verify(procurementApplicationRepository, times(1)).save(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, times(1))
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));
    }

    @Test
    void testSave_ValidationFailed() {
        // 创建无效的测试数据（缺少物料编码）
        ProcurementApplicationEntity invalidEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .availableQuantity(new BigDecimal("400.0000"))
                .build();

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            procurementApplicationService.save(invalidEntity);
        });

        assertEquals("物料编码不能为空", exception.getMessage());

        // 验证Repository方法未被调用
        verify(procurementApplicationRepository, never()).save(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, never())
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));
    }

    @Test
    void testSave_QuantityValidationFailed() {
        // 创建采购量超过可采购量的测试数据
        testEntity.setCurrentPurchaseQuantity(new BigDecimal("900.0000")); // 超过可采购量800
        testEntity.setAvailableQuantity(new BigDecimal("800.0000"));

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            procurementApplicationService.save(testEntity);
        });

        assertEquals("本次采购量不能超过可采购量", exception.getMessage());

        // 验证Repository方法未被调用
        verify(procurementApplicationRepository, never()).save(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, never())
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));
    }

    @Test
    void testSave_DetailQuantitySumValidationFailed() {
        // 修改明细行采购量，使其总和不等于主表采购量
        testEntity.getDetails().get(0).setCurrentPurchaseQuantity(new BigDecimal("200.0000"));
        testEntity.getDetails().get(1).setCurrentPurchaseQuantity(new BigDecimal("200.0000"));
        // 明细总和400，但主表是500

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            procurementApplicationService.save(testEntity);
        });

        assertEquals("明细行采购量之和必须等于主表采购量", exception.getMessage());

        // 验证Repository方法未被调用
        verify(procurementApplicationRepository, never()).save(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, never())
                .updateExecutionBasisPurchasedQuantity(any(ProcurementApplicationEntity.class));
    }

    @Test
    void testGenerateApplicationNo() {
        // 模拟Repository行为
        when(procurementApplicationRepository.generateApplicationNo())
                .thenReturn("PA202508260001");

        // 执行测试
        String applicationNo = procurementApplicationService.generateApplicationNo();

        // 验证结果
        assertEquals("PA202508260001", applicationNo);
        verify(procurementApplicationRepository, times(1)).generateApplicationNo();
    }

    @Test
    void testUpdate_Success() {
        // 准备原始数据
        ProcurementApplicationEntity originalEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("300.0000"))
                .quarter("2025年第一季度")
                .build();

        // 准备更新数据
        ProcurementApplicationEntity updateEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("500.0000")) // 增加200
                .quarter("2025年第一季度")
                .availableQuantity(new BigDecimal("800.0000"))
                .details(Arrays.asList(
                        ProcurementApplicationDetailEntity.builder()
                                .budgetCode("BUD001")
                                .currentPurchaseQuantity(new BigDecimal("250.0000"))
                                .availableQuantity(new BigDecimal("400.0000"))
                                .build(),
                        ProcurementApplicationDetailEntity.builder()
                                .budgetCode("BUD002")
                                .currentPurchaseQuantity(new BigDecimal("250.0000"))
                                .availableQuantity(new BigDecimal("400.0000"))
                                .build()
                ))
                .build();

        // 准备返回数据
        ProcurementApplicationEntity updatedEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .materialName("钢材")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .quarter("2025年第一季度")
                .build();

        // 模拟Repository行为
        when(procurementApplicationRepository.getById("test-app-id")).thenReturn(originalEntity);
        when(procurementApplicationRepository.update(any(ProcurementApplicationEntity.class)))
                .thenReturn(updatedEntity);
        doNothing().when(procurementApplicationRepository)
                .updateExecutionBasisPurchasedQuantityByDelta(any(ProcurementApplicationEntity.class),
                                                             any(ProcurementApplicationEntity.class));

        // 执行测试
        ProcurementApplicationEntity result = procurementApplicationService.update(updateEntity);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-app-id", result.getId());
        assertEquals(new BigDecimal("500.0000"), result.getCurrentPurchaseQuantity());

        // 验证方法调用
        verify(procurementApplicationRepository, times(1)).getById("test-app-id");
        verify(procurementApplicationRepository, times(1)).update(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, times(1))
                .updateExecutionBasisPurchasedQuantityByDelta(eq(originalEntity), any(ProcurementApplicationEntity.class));
    }

    @Test
    void testUpdate_DeltaUpdateFailed() {
        // 准备原始数据
        ProcurementApplicationEntity originalEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .currentPurchaseQuantity(new BigDecimal("300.0000"))
                .quarter("2025年第一季度")
                .build();

        // 准备更新数据
        ProcurementApplicationEntity updateEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .quarter("2025年第一季度")
                .availableQuantity(new BigDecimal("800.0000"))
                .details(Arrays.asList(
                        ProcurementApplicationDetailEntity.builder()
                                .budgetCode("BUD001")
                                .currentPurchaseQuantity(new BigDecimal("250.0000"))
                                .availableQuantity(new BigDecimal("400.0000"))
                                .build()
                ))
                .build();

        // 准备返回数据
        ProcurementApplicationEntity updatedEntity = ProcurementApplicationEntity.builder()
                .id("test-app-id")
                .materialCode("MAT001")
                .currentPurchaseQuantity(new BigDecimal("500.0000"))
                .quarter("2025年第一季度")
                .build();

        // 模拟Repository行为
        when(procurementApplicationRepository.getById("test-app-id")).thenReturn(originalEntity);
        when(procurementApplicationRepository.update(any(ProcurementApplicationEntity.class)))
                .thenReturn(updatedEntity);
        doThrow(new RuntimeException("差量更新采购执行依据失败"))
                .when(procurementApplicationRepository)
                .updateExecutionBasisPurchasedQuantityByDelta(any(ProcurementApplicationEntity.class),
                                                             any(ProcurementApplicationEntity.class));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            procurementApplicationService.update(updateEntity);
        });

        assertTrue(exception.getMessage().contains("更新采购申请成功，但差量更新采购执行依据失败"));

        // 验证方法调用
        verify(procurementApplicationRepository, times(1)).getById("test-app-id");
        verify(procurementApplicationRepository, times(1)).update(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, times(1))
                .updateExecutionBasisPurchasedQuantityByDelta(any(ProcurementApplicationEntity.class),
                                                             any(ProcurementApplicationEntity.class));
    }

    @Test
    void testUpdate_EntityNotFound() {
        // 准备更新数据
        ProcurementApplicationEntity updateEntity = ProcurementApplicationEntity.builder()
                .id("non-existent-id")
                .materialCode("MAT001")
                .build();

        // 模拟Repository行为 - 返回null表示不存在
        when(procurementApplicationRepository.getById("non-existent-id")).thenReturn(null);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            procurementApplicationService.update(updateEntity);
        });

        assertEquals("采购申请不存在", exception.getMessage());

        // 验证方法调用
        verify(procurementApplicationRepository, times(1)).getById("non-existent-id");
        verify(procurementApplicationRepository, never()).update(any(ProcurementApplicationEntity.class));
        verify(procurementApplicationRepository, never())
                .updateExecutionBasisPurchasedQuantityByDelta(any(ProcurementApplicationEntity.class),
                                                             any(ProcurementApplicationEntity.class));
    }
}
