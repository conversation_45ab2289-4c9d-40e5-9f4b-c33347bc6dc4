package com.cdkit.modules.cm.application.procurement;

import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementApplicationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 采购申请应用服务
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcurementApplicationService {

    private final ProcurementApplicationRepository procurementApplicationRepository;

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     * 
     * @return 采购申请单号
     */
    public String generateApplicationNo() {
        return procurementApplicationRepository.generateApplicationNo();
    }

    /**
     * 保存采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 保存后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcurementApplicationEntity save(ProcurementApplicationEntity entity) {
        // 参数校验
        validateProcurementApplication(entity);
        
        // 计算价格信息
        entity.calculatePrices();
        
        // 计算明细价格
        if (entity.getDetails() != null) {
            entity.getDetails().forEach(detail -> detail.calculatePrices());
        }
        
        // 保存到数据库
        return procurementApplicationRepository.save(entity);
    }

    /**
     * 更新采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 更新后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcurementApplicationEntity update(ProcurementApplicationEntity entity) {
        // 检查申请是否存在
        ProcurementApplicationEntity existingEntity = procurementApplicationRepository.getById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("采购申请不存在");
        }
        
        // 参数校验
        validateProcurementApplication(entity);
        
        // 计算价格信息
        entity.calculatePrices();
        
        // 计算明细价格
        if (entity.getDetails() != null) {
            entity.getDetails().forEach(detail -> detail.calculatePrices());
        }
        
        // 更新到数据库
        return procurementApplicationRepository.update(entity);
    }

    /**
     * 根据ID查询采购申请详情（包含明细）
     * 
     * @param id 采购申请ID
     * @return 采购申请实体
     */
    public ProcurementApplicationEntity getById(String id) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("采购申请ID不能为空");
        }
        return procurementApplicationRepository.getById(id);
    }

    /**
     * 根据ID删除采购申请
     * 
     * @param id 采购申请ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("采购申请ID不能为空");
        }
        procurementApplicationRepository.deleteById(id);
    }

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("采购申请ID列表不能为空");
        }
        procurementApplicationRepository.deleteBatch(ids);
    }

    /**
     * 根据申请单号查询采购申请
     * 
     * @param applicationNo 申请单号
     * @return 采购申请实体
     */
    public ProcurementApplicationEntity findByApplicationNo(String applicationNo) {
        if (applicationNo == null || applicationNo.trim().isEmpty()) {
            throw new IllegalArgumentException("申请单号不能为空");
        }
        return procurementApplicationRepository.findByApplicationNo(applicationNo);
    }

    /**
     * 加权均分本次采购量到明细行
     * 
     * @param entity 采购申请实体
     * @return 分配后的实体
     */
    public ProcurementApplicationEntity distributeQuantityToDetails(ProcurementApplicationEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("采购申请实体不能为空");
        }
        
        entity.distributeQuantityToDetails();
        return entity;
    }

    /**
     * 校验采购申请数据
     * 
     * @param entity 采购申请实体
     */
    private void validateProcurementApplication(ProcurementApplicationEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("采购申请实体不能为空");
        }

        // 校验基本信息
        if (entity.getMaterialCode() == null || entity.getMaterialCode().trim().isEmpty()) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
        if (entity.getMaterialName() == null || entity.getMaterialName().trim().isEmpty()) {
            throw new IllegalArgumentException("物料名称不能为空");
        }
        if (entity.getCurrentPurchaseQuantity() == null || entity.getCurrentPurchaseQuantity().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("本次采购量必须大于0");
        }

        // 校验本次采购量不能超过可采购量
        if (!entity.validatePurchaseQuantity()) {
            throw new IllegalArgumentException("本次采购量不能超过可采购量");
        }

        // 校验明细数据
        if (entity.getDetails() == null || entity.getDetails().isEmpty()) {
            throw new IllegalArgumentException("明细列表不能为空");
        }

        // 校验明细行采购量之和是否等于主表采购量
        if (!entity.validateDetailQuantitySum()) {
            throw new IllegalArgumentException("明细行采购量之和必须等于主表采购量");
        }

        // 校验每个明细行的采购量不能超过可采购量
        for (int i = 0; i < entity.getDetails().size(); i++) {
            var detail = entity.getDetails().get(i);
            if (!detail.validatePurchaseQuantity()) {
                throw new IllegalArgumentException("第" + (i + 1) + "行明细的本次采购量不能超过可采购量");
            }
        }
    }
}
