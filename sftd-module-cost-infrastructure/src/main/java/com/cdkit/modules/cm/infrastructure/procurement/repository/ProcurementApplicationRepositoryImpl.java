package com.cdkit.modules.cm.infrastructure.procurement.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationDetailEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementApplicationRepository;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplication;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationDetailService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisDetailService;
import com.cdkit.modules.cm.infrastructure.procurement.converter.ProcurementApplicationConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购申请仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProcurementApplicationRepositoryImpl implements ProcurementApplicationRepository {

    private final ICostProcurementApplicationService costProcurementApplicationService;
    private final ICostProcurementApplicationDetailService costProcurementApplicationDetailService;
    private final ICostProcurementExecutionBasisService costProcurementExecutionBasisService;
    private final ICostProcurementExecutionBasisDetailService costProcurementExecutionBasisDetailService;

    @Override
    public ProcurementApplicationEntity save(ProcurementApplicationEntity entity) {
        // 转换为基础设施实体
        CostProcurementApplication infraEntity = ProcurementApplicationConverter.toInfrastructure(entity);
        
        // 如果没有申请单号，自动生成
        if (infraEntity.getApplicationNo() == null || infraEntity.getApplicationNo().trim().isEmpty()) {
            infraEntity.setApplicationNo(generateApplicationNo());
        }
        
        // 保存主表
        costProcurementApplicationService.save(infraEntity);
        
        // 保存明细
        if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
            List<CostProcurementApplicationDetail> detailList = entity.getDetails().stream()
                    .map(detail -> {
                        CostProcurementApplicationDetail infraDetail = ProcurementApplicationConverter.toInfrastructureDetail(detail);
                        infraDetail.setApplicationId(infraEntity.getId());
                        return infraDetail;
                    })
                    .collect(Collectors.toList());
            
            costProcurementApplicationDetailService.saveBatch(detailList);
        }
        
        // 返回保存后的实体
        return getById(infraEntity.getId());
    }

    @Override
    public ProcurementApplicationEntity update(ProcurementApplicationEntity entity) {
        // 转换为基础设施实体
        CostProcurementApplication infraEntity = ProcurementApplicationConverter.toInfrastructure(entity);
        
        // 更新主表
        costProcurementApplicationService.updateById(infraEntity);
        
        // 删除原有明细
        LambdaQueryWrapper<CostProcurementApplicationDetail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(CostProcurementApplicationDetail::getApplicationId, entity.getId());
        costProcurementApplicationDetailService.remove(deleteWrapper);
        
        // 保存新明细
        if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
            List<CostProcurementApplicationDetail> detailList = entity.getDetails().stream()
                    .map(detail -> {
                        CostProcurementApplicationDetail infraDetail = ProcurementApplicationConverter.toInfrastructureDetail(detail);
                        infraDetail.setApplicationId(entity.getId());
                        infraDetail.setId(null); // 重新生成ID
                        return infraDetail;
                    })
                    .collect(Collectors.toList());
            
            costProcurementApplicationDetailService.saveBatch(detailList);
        }
        
        // 返回更新后的实体
        return getById(entity.getId());
    }

    @Override
    public ProcurementApplicationEntity getById(String id) {
        // 查询主表
        CostProcurementApplication infraEntity = costProcurementApplicationService.getById(id);
        if (infraEntity == null) {
            return null;
        }
        
        // 查询明细
        LambdaQueryWrapper<CostProcurementApplicationDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CostProcurementApplicationDetail::getApplicationId, id)
                .eq(CostProcurementApplicationDetail::getDelFlag, 0)
                .orderByAsc(CostProcurementApplicationDetail::getCreateTime);
        
        List<CostProcurementApplicationDetail> detailList = costProcurementApplicationDetailService.list(detailWrapper);
        
        // 转换为领域实体
        ProcurementApplicationEntity domainEntity = ProcurementApplicationConverter.toDomain(infraEntity);
        if (detailList != null && !detailList.isEmpty()) {
            List<ProcurementApplicationDetailEntity> domainDetailList = detailList.stream()
                    .map(ProcurementApplicationConverter::toDomainDetail)
                    .collect(Collectors.toList());
            domainEntity.setDetails(domainDetailList);
        }
        
        return domainEntity;
    }

    @Override
    public void deleteById(String id) {
        // 删除明细（逻辑删除）
        LambdaQueryWrapper<CostProcurementApplicationDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CostProcurementApplicationDetail::getApplicationId, id);
        costProcurementApplicationDetailService.remove(detailWrapper);
        
        // 删除主表（逻辑删除）
        costProcurementApplicationService.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        for (String id : ids) {
            deleteById(id);
        }
    }

    @Override
    public String generateApplicationNo() {
        // 格式：PA + YYYYMMDD + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "PA" + dateStr;
        
        // 查询当天已有的最大序号
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(CostProcurementApplication::getApplicationNo, prefix)
                .eq(CostProcurementApplication::getDelFlag, 0)
                .orderByDesc(CostProcurementApplication::getApplicationNo)
                .last("LIMIT 1");
        
        CostProcurementApplication lastApplication = costProcurementApplicationService.getOne(queryWrapper);
        
        int nextSequence = 1;
        if (lastApplication != null && lastApplication.getApplicationNo() != null) {
            String lastNo = lastApplication.getApplicationNo();
            if (lastNo.length() >= 14) { // PA + 8位日期 + 4位序号
                try {
                    String sequenceStr = lastNo.substring(10); // 获取后4位序号
                    int lastSequence = Integer.parseInt(sequenceStr);
                    nextSequence = lastSequence + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析申请单号序号失败: {}", lastNo, e);
                }
            }
        }
        
        return prefix + String.format("%04d", nextSequence);
    }

    @Override
    public ProcurementApplicationEntity findByApplicationNo(String applicationNo) {
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProcurementApplication::getApplicationNo, applicationNo)
                .eq(CostProcurementApplication::getDelFlag, 0);
        
        CostProcurementApplication infraEntity = costProcurementApplicationService.getOne(queryWrapper);
        if (infraEntity == null) {
            return null;
        }
        
        return getById(infraEntity.getId());
    }

    @Override
    public boolean existsByApplicationNo(String applicationNo) {
        LambdaQueryWrapper<CostProcurementApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProcurementApplication::getApplicationNo, applicationNo)
                .eq(CostProcurementApplication::getDelFlag, 0);
        
        return costProcurementApplicationService.count(queryWrapper) > 0;
    }

    @Override
    public void updateExecutionBasisPurchasedQuantity(ProcurementApplicationEntity entity) {
        if (entity == null || entity.getMaterialCode() == null || entity.getQuarter() == null) {
            log.warn("采购申请实体或关键字段为空，无法更新采购执行依据已采购量");
            return;
        }

        try {
            // 1. 查找对应的采购执行依据主表
            LambdaQueryWrapper<CostProcurementExecutionBasis> mainWrapper = new LambdaQueryWrapper<>();
            mainWrapper.eq(CostProcurementExecutionBasis::getMaterialCode, entity.getMaterialCode())
                    .eq(CostProcurementExecutionBasis::getQuarter, entity.getQuarter())
                    .eq(CostProcurementExecutionBasis::getDelFlag, 0);

            CostProcurementExecutionBasis executionBasis = costProcurementExecutionBasisService.getOne(mainWrapper);
            if (executionBasis == null) {
                log.warn("未找到对应的采购执行依据，物料编码: {}, 季度: {}",
                        entity.getMaterialCode(), entity.getQuarter());
                return;
            }

            // 2. 更新主表已采购量
            BigDecimal currentPurchased = executionBasis.getPurchasedQuantity() != null ?
                    executionBasis.getPurchasedQuantity() : BigDecimal.ZERO;
            BigDecimal newPurchased = currentPurchased.add(entity.getCurrentPurchaseQuantity());
            executionBasis.setPurchasedQuantity(newPurchased);
            costProcurementExecutionBasisService.updateById(executionBasis);

            log.info("更新采购执行依据主表已采购量成功，ID: {}, 原已采购量: {}, 本次采购量: {}, 新已采购量: {}",
                    executionBasis.getId(), currentPurchased, entity.getCurrentPurchaseQuantity(), newPurchased);

            // 3. 更新明细表已采购量
            if (entity.getDetails() != null && !entity.getDetails().isEmpty()) {
                for (ProcurementApplicationDetailEntity detail : entity.getDetails()) {
                    updateExecutionBasisDetailPurchasedQuantity(executionBasis.getId(), detail);
                }
            }

        } catch (Exception e) {
            log.error("更新采购执行依据已采购量失败，物料编码: {}, 季度: {}",
                    entity.getMaterialCode(), entity.getQuarter(), e);
            throw new RuntimeException("更新采购执行依据已采购量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新采购执行依据明细表的已采购量
     */
    private void updateExecutionBasisDetailPurchasedQuantity(String executionBasisId,
                                                           ProcurementApplicationDetailEntity detail) {
        if (detail == null || detail.getBudgetCode() == null) {
            log.warn("采购申请明细或预算编码为空，跳过更新");
            return;
        }

        try {
            // 查找对应的采购执行依据明细
            LambdaQueryWrapper<CostProcurementExecutionBasisDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(CostProcurementExecutionBasisDetail::getExecutionBasisId, executionBasisId)
                    .eq(CostProcurementExecutionBasisDetail::getBudgetCode, detail.getBudgetCode())
                    .eq(CostProcurementExecutionBasisDetail::getDelFlag, 0);

            CostProcurementExecutionBasisDetail executionDetail = costProcurementExecutionBasisDetailService.getOne(detailWrapper);
            if (executionDetail == null) {
                log.warn("未找到对应的采购执行依据明细，执行依据ID: {}, 预算编码: {}",
                        executionBasisId, detail.getBudgetCode());
                return;
            }

            // 更新明细已采购量
            BigDecimal currentDetailPurchased = executionDetail.getPurchasedQuantity() != null ?
                    executionDetail.getPurchasedQuantity() : BigDecimal.ZERO;
            BigDecimal newDetailPurchased = currentDetailPurchased.add(detail.getCurrentPurchaseQuantity());
            executionDetail.setPurchasedQuantity(newDetailPurchased);
            costProcurementExecutionBasisDetailService.updateById(executionDetail);

            log.info("更新采购执行依据明细已采购量成功，ID: {}, 预算编码: {}, 原已采购量: {}, 本次采购量: {}, 新已采购量: {}",
                    executionDetail.getId(), detail.getBudgetCode(), currentDetailPurchased,
                    detail.getCurrentPurchaseQuantity(), newDetailPurchased);

        } catch (Exception e) {
            log.error("更新采购执行依据明细已采购量失败，执行依据ID: {}, 预算编码: {}",
                    executionBasisId, detail.getBudgetCode(), e);
            throw new RuntimeException("更新采购执行依据明细已采购量失败: " + e.getMessage(), e);
        }
    }
}
