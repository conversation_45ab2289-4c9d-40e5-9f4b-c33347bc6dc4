package com.cdkit.modules.cm.infrastructure.procurement.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.util.SpringContextUtils;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.repository.CostProcurementExecutionBasisRepository;
import com.cdkit.modules.cm.infrastructure.procurement.CostProcurementExecutionBasisInfraConverter;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisService;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购执行依据仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostProcurementExecutionBasisRepositoryImpl implements CostProcurementExecutionBasisRepository {

    private final ICostProcurementExecutionBasisService costProcurementExecutionBasisService;
    private final ICostProcurementExecutionBasisDetailService costProcurementExecutionBasisDetailService;

    @Override
    public PageRes<CostProcurementExecutionBasisEntity> queryPageList(CostProcurementExecutionBasisEntity queryEntity, PageReq pageReq) {
        CostProcurementExecutionBasis convert = CostProcurementExecutionBasisInfraConverter.convert(queryEntity);

        LambdaQueryWrapper<CostProcurementExecutionBasis> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加模糊查询条件
        queryWrapper.like(StrUtil.isNotEmpty(convert.getMaterialName()), CostProcurementExecutionBasis::getMaterialName, convert.getMaterialName());
        queryWrapper.like(StrUtil.isNotEmpty(convert.getMaterialCode()), CostProcurementExecutionBasis::getMaterialCode, convert.getMaterialCode());
        
        // 添加时间查询条件
        if (convert.getStartDate() != null) {
            queryWrapper.ge(CostProcurementExecutionBasis::getStartDate, convert.getStartDate());
        }
        if (convert.getEndDate() != null) {
            queryWrapper.le(CostProcurementExecutionBasis::getEndDate, convert.getEndDate());
        }
        
        // 添加季度查询条件
        queryWrapper.eq(StrUtil.isNotEmpty(convert.getQuarter()), CostProcurementExecutionBasis::getQuarter, convert.getQuarter());

        Page<CostProcurementExecutionBasis> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());

        // 添加排序
        if (pageReq.getOrderParam() != null && !pageReq.getOrderParam().isEmpty()) {
            pageReq.getOrderParam().forEach(orderParam -> {
                if ("desc".equalsIgnoreCase(orderParam.getOrder())) {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByDesc(CostProcurementExecutionBasis::getCreateTime);
                    }
                } else {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByAsc(CostProcurementExecutionBasis::getCreateTime);
                    }
                }
            });
        } else {
            // 默认按创建时间倒序
            queryWrapper.orderByDesc(CostProcurementExecutionBasis::getCreateTime);
        }

        Page<CostProcurementExecutionBasis> pageList = costProcurementExecutionBasisService.page(page, queryWrapper);
        List<CostProcurementExecutionBasis> records = pageList.getRecords();
        
        // 转换为领域实体并加载明细
        List<CostProcurementExecutionBasisEntity> domainEntities = records.stream()
                .map(this::convertToDomainWithDetails)
                .collect(Collectors.toList());

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), domainEntities, pageList.getTotal(), pageList.getPages());
    }

    @Override
    public CostProcurementExecutionBasisEntity getById(String id) {
        CostProcurementExecutionBasis infraEntity = costProcurementExecutionBasisService.getById(id);
        if (infraEntity == null) {
            return null;
        }
        return convertToDomainWithDetails(infraEntity);
    }

    @Override
    public CostProcurementExecutionBasisEntity save(CostProcurementExecutionBasisEntity entity) {
        CostProcurementExecutionBasis infraEntity = CostProcurementExecutionBasisInfraConverter.convert(entity);
        List<CostProcurementExecutionBasisDetail> detailList = null;
        
        if (entity.getCostProcurementExecutionBasisDetailList() != null) {
            detailList = entity.getCostProcurementExecutionBasisDetailList().stream()
                    .map(CostProcurementExecutionBasisInfraConverter::convertDetail)
                    .collect(Collectors.toList());
        }
        
        costProcurementExecutionBasisService.saveMain(infraEntity, detailList);
        return convertToDomainWithDetails(infraEntity);
    }

    @Override
    public CostProcurementExecutionBasisEntity update(CostProcurementExecutionBasisEntity entity) {
        CostProcurementExecutionBasis infraEntity = CostProcurementExecutionBasisInfraConverter.convert(entity);
        List<CostProcurementExecutionBasisDetail> detailList = null;
        
        if (entity.getCostProcurementExecutionBasisDetailList() != null) {
            detailList = entity.getCostProcurementExecutionBasisDetailList().stream()
                    .map(CostProcurementExecutionBasisInfraConverter::convertDetail)
                    .collect(Collectors.toList());
        }
        
        costProcurementExecutionBasisService.updateMain(infraEntity, detailList);
        return convertToDomainWithDetails(infraEntity);
    }

    @Override
    public void deleteById(String id) {
        costProcurementExecutionBasisService.delMain(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        costProcurementExecutionBasisService.delBatchMain(ids);
    }

    @Override
    public List<CostProcurementExecutionBasisEntity> findAll(CostProcurementExecutionBasisEntity queryEntity) {
        CostProcurementExecutionBasis convert = CostProcurementExecutionBasisInfraConverter.convert(queryEntity);
        
        LambdaQueryWrapper<CostProcurementExecutionBasis> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.like(StrUtil.isNotEmpty(convert.getMaterialName()), CostProcurementExecutionBasis::getMaterialName, convert.getMaterialName());
        queryWrapper.like(StrUtil.isNotEmpty(convert.getMaterialCode()), CostProcurementExecutionBasis::getMaterialCode, convert.getMaterialCode());
        queryWrapper.eq(StrUtil.isNotEmpty(convert.getQuarter()), CostProcurementExecutionBasis::getQuarter, convert.getQuarter());
        
        if (convert.getStartDate() != null) {
            queryWrapper.ge(CostProcurementExecutionBasis::getStartDate, convert.getStartDate());
        }
        if (convert.getEndDate() != null) {
            queryWrapper.le(CostProcurementExecutionBasis::getEndDate, convert.getEndDate());
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(CostProcurementExecutionBasis::getCreateTime);
        
        List<CostProcurementExecutionBasis> infraEntities = costProcurementExecutionBasisService.list(queryWrapper);
        
        return infraEntities.stream()
                .map(this::convertToDomainWithDetails)
                .collect(Collectors.toList());
    }

    /**
     * 转换为领域实体并加载明细
     */
    private CostProcurementExecutionBasisEntity convertToDomainWithDetails(CostProcurementExecutionBasis infraEntity) {
        CostProcurementExecutionBasisEntity domainEntity = CostProcurementExecutionBasisInfraConverter.convertToDomain(infraEntity);
        
        // 加载明细数据
        LambdaQueryWrapper<CostProcurementExecutionBasisDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(CostProcurementExecutionBasisDetail::getExecutionBasisId, infraEntity.getId())
                .eq(CostProcurementExecutionBasisDetail::getDelFlag, 0)
                .orderByAsc(CostProcurementExecutionBasisDetail::getCreateTime);
        
        List<CostProcurementExecutionBasisDetail> detailList = costProcurementExecutionBasisDetailService.list(detailQueryWrapper);
        
        if (detailList != null && !detailList.isEmpty()) {
            List<com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisDetailEntity> domainDetailList = 
                    detailList.stream()
                            .map(CostProcurementExecutionBasisInfraConverter::convertDetailToDomain)
                            .collect(Collectors.toList());
            domainEntity.setCostProcurementExecutionBasisDetailList(domainDetailList);
        }
        
        return domainEntity;
    }
}
