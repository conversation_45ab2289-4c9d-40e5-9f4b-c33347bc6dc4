package com.cdkit.modules.cm.performance.procurement;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationDTO;
import com.cdkit.modules.cm.application.procurement.ProcurementApplicationService;
import com.cdkit.modules.cm.application.procurement.converter.ProcurementApplicationDTOConverter;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 采购申请控制器
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Tag(name = "采购申请管理", description = "采购申请相关接口")
@RestController
@RequestMapping("/cost/procurementApplication")
@RequiredArgsConstructor
@Slf4j
public class ProcurementApplicationController {

    private final ProcurementApplicationService procurementApplicationService;

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     * 
     * @return 采购申请单号
     */
    @Operation(summary = "采购申请-生成申请单号", description = "自动生成采购申请单号，格式：PA + YYYYMMDD + 4位序号")
    @GetMapping("/generateApplicationNo")
    public Result<String> generateApplicationNo() {
        try {
            String applicationNo = procurementApplicationService.generateApplicationNo();
            return Result.OK(applicationNo);
        } catch (Exception e) {
            log.error("生成采购申请单号失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 新增采购申请
     * 
     * @param dto 采购申请DTO
     * @return 保存后的采购申请信息
     */
    @Operation(summary = "采购申请-新增", description = "创建新的采购申请，支持保存草稿和提交审批两种操作")
    @PostMapping
    public Result<ProcurementApplicationDTO> create(@Valid @RequestBody ProcurementApplicationDTO dto) {
        try {
            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 保存
            ProcurementApplicationEntity savedEntity = procurementApplicationService.save(entity);
            
            // 转换为DTO返回
            ProcurementApplicationDTO resultDTO = ProcurementApplicationDTOConverter.toDTO(savedEntity);
            return Result.OK(resultDTO);
            
        } catch (IllegalArgumentException e) {
            log.warn("新增采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增采购申请失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 编辑采购申请
     * 
     * @param id 采购申请ID
     * @param dto 采购申请DTO
     * @return 更新后的采购申请信息
     */
    @Operation(summary = "采购申请-编辑", description = "修改现有的采购申请信息")
    @PutMapping("/{id}")
    public Result<ProcurementApplicationDTO> update(
            @Parameter(description = "采购申请ID", required = true) @PathVariable String id,
            @Valid @RequestBody ProcurementApplicationDTO dto) {
        try {
            // 设置ID
            dto.setId(id);
            
            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 更新
            ProcurementApplicationEntity updatedEntity = procurementApplicationService.update(entity);
            
            // 转换为DTO返回
            ProcurementApplicationDTO resultDTO = ProcurementApplicationDTOConverter.toDTO(updatedEntity);
            return Result.OK(resultDTO);
            
        } catch (IllegalArgumentException e) {
            log.warn("编辑采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("编辑采购申请失败，ID: {}", id, e);
            return Result.error("编辑失败: " + e.getMessage());
        }
    }

    /**
     * 数据回填接口
     * 根据采购申请ID查询完整的申请信息，用于编辑页面数据回填
     * 
     * @param id 采购申请ID
     * @return 采购申请详情
     */
    @Operation(summary = "采购申请-详情查询", description = "根据采购申请ID查询完整的申请信息，用于编辑页面数据回填")
    @GetMapping("/{id}")
    public Result<ProcurementApplicationDTO> getById(
            @Parameter(description = "采购申请ID", required = true) @PathVariable String id) {
        try {
            ProcurementApplicationEntity entity = procurementApplicationService.getById(id);
            if (entity == null) {
                return Result.error("采购申请不存在");
            }
            
            ProcurementApplicationDTO dto = ProcurementApplicationDTOConverter.toDTO(entity);
            return Result.OK(dto);
            
        } catch (IllegalArgumentException e) {
            log.warn("查询采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询采购申请详情失败，ID: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除采购申请
     * 
     * @param id 采购申请ID
     * @return 删除结果
     */
    @Operation(summary = "采购申请-删除", description = "根据ID删除采购申请")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "采购申请ID", required = true) @PathVariable String id) {
        try {
            procurementApplicationService.deleteById(id);
            return Result.OK();
            
        } catch (IllegalArgumentException e) {
            log.warn("删除采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除采购申请失败，ID: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     * @return 删除结果
     */
    @Operation(summary = "采购申请-批量删除", description = "批量删除采购申请")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<String> ids) {
        try {
            procurementApplicationService.deleteBatch(ids);
            return Result.OK();
            
        } catch (IllegalArgumentException e) {
            log.warn("批量删除采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("批量删除采购申请失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 加权均分本次采购量到明细行
     * 
     * @param dto 采购申请DTO
     * @return 分配后的采购申请信息
     */
    @Operation(summary = "采购申请-加权均分", description = "将主表的本次采购量按预算总量比例分配到明细行")
    @PostMapping("/distributeQuantity")
    public Result<ProcurementApplicationDTO> distributeQuantityToDetails(@Valid @RequestBody ProcurementApplicationDTO dto) {
        try {
            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 执行加权均分
            ProcurementApplicationEntity distributedEntity = procurementApplicationService.distributeQuantityToDetails(entity);
            
            // 转换为DTO返回
            ProcurementApplicationDTO resultDTO = ProcurementApplicationDTOConverter.toDTO(distributedEntity);
            return Result.OK(resultDTO);
            
        } catch (IllegalArgumentException e) {
            log.warn("加权均分参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("加权均分失败", e);
            return Result.error("加权均分失败: " + e.getMessage());
        }
    }

    /**
     * 根据申请单号查询采购申请
     * 
     * @param applicationNo 申请单号
     * @return 采购申请详情
     */
    @Operation(summary = "采购申请-根据申请单号查询", description = "根据申请单号查询采购申请详情")
    @GetMapping("/findByApplicationNo")
    public Result<ProcurementApplicationDTO> findByApplicationNo(
            @Parameter(description = "申请单号", required = true) @RequestParam String applicationNo) {
        try {
            ProcurementApplicationEntity entity = procurementApplicationService.findByApplicationNo(applicationNo);
            if (entity == null) {
                return Result.error("采购申请不存在");
            }
            
            ProcurementApplicationDTO dto = ProcurementApplicationDTOConverter.toDTO(entity);
            return Result.OK(dto);
            
        } catch (IllegalArgumentException e) {
            log.warn("根据申请单号查询采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据申请单号查询采购申请失败，申请单号: {}", applicationNo, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
